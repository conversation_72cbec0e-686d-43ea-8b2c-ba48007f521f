<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>

<!-- 考试选择内容组件 -->
<view class="popu-content-c">
  <view class="exam-list">
    <view hover-class="none" class="exam-list-item {{filterUtils.isFilterItemOptionSelected(item.value, tempSelected) ? 'active' : ''}}" wx:for="{{examList[0].list}}" wx:key="{{uniqueKey}}" bindtap="handleExamSelect" data-value="{{item.value}}">
      {{item.name}}
    </view>
  </view>
</view>

<popu-bottom bind:reset="handleReset" isEducation="{{filterKey=='education'?true:false}}" bind:confirm="handleConfirm" />