const ROUTER = require("@/services/mpRouter")
const PopupMenuFilterMixin = require("../../../components/popup/popup-menu-filter/mixin")
const API = require("../../../config/api")
const UTIL = require("../../../utils/util")
const { processMenuList } = require("@/services/menuServices")
const { handleMultiSelect } = require("@/services/selectionService")
const APP = getApp()
const {
  setJobSelectForTemplateCache,
  getJobSelectForTemplateCache,
  getNoticeSelectForTemplateCache,
  setNoticeSelectForTemplateCache,
} = require("@/utils/cache/filterCache")
const { getSelectedRegionsCache } = require("@/utils/cache/regionCache")

const pageConfig = Object.assign({}, PopupMenuFilterMixin, {
  data: {
    // 页面滚动控制
    pageScrollDisabled: false,
    pageContainerStyle: "",
    // 菜单相关数据
    menuList: [],
    // 弹窗相关
    showPopupFilterMenu: false,
    overlayTop: 0,
    // 弹窗相关属性
    showFooter: false,
    show_white: false,
    jobMenuData: {},
    jobSelectForTemplate: {},
    activeExpanded: "",
    isRequest: false,
    jobList: [],
    page: 1,
    // 分页相关状态
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    jobData: {},
    isShowResume: false,
    isPageLoadComplete: false, // 页面是否加载完成
    isLogin: false,
  },

  async onLoad(options) {
    await APP.checkLoadRequest()
    // 初始化弹窗混入
    this.initPopupMenuFilterMixin()

    await this.getJobFilterMenu()
    // 页面渲染完成后获取导航栏高度并设置sticky的top值
    this.setMenuStickyTop()

    // 从缓存恢复筛选条件
    await this.restoreFilterFromCache()

    this.applyFilter()
  },
  async onShow() {
    console.log("页面显示，更新筛选条件")
    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }

    // 保存当前的筛选条件，用于后续比较
    const beforeFilterConditions = JSON.stringify(
      this.data.jobSelectForTemplate
    )

    await this.getJobFilterMenu()
    // 页面渲染完成后获取导航栏高度并设置sticky的top值
    this.setMenuStickyTop()

    // 从缓存恢复筛选条件
    await this.restoreFilterFromCache()

    // 比较筛选条件是否发生变化
    const afterFilterConditions = JSON.stringify(this.data.jobSelectForTemplate)
    const isFilterChanged = beforeFilterConditions !== afterFilterConditions

    if (isFilterChanged) {
      console.log("筛选条件发生变化，重新请求数据")
      this.applyFilter()
    }
  },

  async getJobFilterMenu() {
    try {
      const res = await UTIL.request(API.getJobFilterMenu)
      if (res && res.error && res.error.code === 0 && res.data) {
        const isAll = {
          is_default: 1,
          name: "全部",
          region_city: 0,
          region_district: 0,
          region_province: 0,
          short_name: "",
          value: "0-0-0",
        }

        // 处理数据：将 key 换成 value，并在数组开头添加 isAll
        const processedData = getSelectedRegionsCache("announcement").map(
          (item) => ({
            ...item,
            name: item.text || item.area_name,
            value: item.key,
          })
        )
        this.setData({
          jobData: res.data,
          isShowResume:
            res.data?.complete_progress?.is_tips === 1 &&
            !APP.globalData.hasResume,
          isPageLoadComplete: true,
          isLogin: APP.getIsLogin(),
          cachedRegions: [isAll, ...processedData],
        })
        // 初始化动态菜单
        if (res.data.job_filter_menu) {
          this.initDynamicMenu(res.data.job_filter_menu)
        }
      } else {
      }
    } catch (error) {}
  },
  closeNote() {
    this.setData({
      isShowResume: false,
    })
    APP.globalData.hasResume = true
  },
  /**
   * 初始化动态菜单
   * @param {Array} serverMenuList 服务器返回的菜单列表
   */
  initDynamicMenu(serverMenuList) {
    if (serverMenuList && serverMenuList.length) {
      // 使用提取后的纯函数处理菜单
      const menuList = processMenuList(serverMenuList)
      const jobMenuData = {}
      serverMenuList.forEach((item) => {
        jobMenuData[item.filter_key] = item
      })
      this.setData({
        jobMenuData,
        menuList,
        jobSelectForTemplate: this.getNoticeSelectFromMenuData(serverMenuList),
      })
    }
  },
  getNoticeSelectFromMenuData(serverMenuList) {
    const result = {}

    serverMenuList.forEach((menu) => {
      const filterKey = menu.filter_key

      if (filterKey === "filter_list") {
        result[filterKey] = {}

        menu.data.forEach((category) => {
          if (category.list) {
            category.list.forEach((filterGroup) => {
              const groupFilterKey = filterGroup.filter_key
              if (groupFilterKey) {
                // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                result[filterKey][groupFilterKey] = []
              }
            })
          }
        })
      } else if (filterKey) {
        result[filterKey] = []
      }
    })
    console.log("result", result)
    return result
  },
  /**
   * 构造API请求参数 - 直接从 noticeSelectForTemplate 构建
   * @returns {Object} API参数对象
   */
  buildApiParams(selectedData) {
    const apiParams = {}

    Object.keys(selectedData).forEach((keyName) => {
      const data =
        selectedData[keyName] || (keyName !== "filter_list" ? [] : {})

      if (keyName === "fit_me" || keyName === "has_tenure") {
        apiParams[keyName] = data[0] || null
      }
      if (keyName === "exam_type") {
        apiParams[keyName] = data
      }

      if (keyName === "apply_region") {
        if (data[0] == "0-0-0") {
          // 从 list 中提取所有有 key 值的项目，组成 key 值数组
          const regionList =
            this.data.jobMenuData.apply_region.data[0].list || []
          const keyArray = regionList
            .filter((item) => item && item.key) // 过滤掉没有 key 的项目
            .map((item) => item.key) // 提取 key 值

          apiParams["region"] = keyArray

          console.log("提取的地区 key 数组:", keyArray)
        } else {
          apiParams["region"] = data
        }
      }
      if (keyName === "filter_list") {
        Object.keys(data).forEach((filterKey) => {
          const filterValue = data[filterKey] || []

          // 招录人数 - 转换为数字
          if (filterKey === "need_num") {
            const numValue = Number(filterValue[0])
            const val = !isNaN(numValue) ? numValue : null
            apiParams[filterKey] = val
          } else {
            apiParams[filterKey] = filterValue
          }
        })
      }
    })
    return UTIL.convertArraysToString(apiParams)
  },
  /**
   * 处理地区列表项点击
   * @param {Object} e 事件对象
   */
  handleRegionItemClick(e) {
    const { item } = e.currentTarget.dataset
    const { jobSelectForTemplate } = this.data
    let cacheNoticeSelectForTemplate =
      getNoticeSelectForTemplateCache("announcement")
    cacheNoticeSelectForTemplate.apply_region = [item.value]
    jobSelectForTemplate.apply_region = [item.value]
    this.setData({
      jobSelectForTemplate,
    })
    setJobSelectForTemplateCache(this.data.jobSelectForTemplate)
    setNoticeSelectForTemplateCache(
      cacheNoticeSelectForTemplate,
      "announcement"
    )

    // 应用筛选
    this.applyFilter()
  },
  /**
   * 应用筛选条件 - 统一处理两个Tab
   */
  async applyFilter() {
    // 重置分页状态，但不清空jobList，避免缺省图闪现
    this.setData({
      page: 1,
      hasMore: true,
      // jobList: [], // 注释掉，避免缺省图闪现
    })

    const apiParams = this.buildApiParams(this.data.jobSelectForTemplate)
    console.log("拿到的参数", apiParams)
    await this.getJobList(apiParams, false)
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    })
  },

  async getJobList(filterConditions = {}, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.isLoading ||
      (!isLoadMore && !this.data.hasMore && this.data.page > 1)
    ) {
      return
    }

    try {
      // 确保传递的参数不为空，至少是一个空对象
      const requestParams = {
        page: this.data.page,
        ...filterConditions,
      }

      this.setData({
        isLoading: true,
      })

      const res = await UTIL.request(API.getJobList, requestParams)

      if (res && res.error && res.error.code === 0 && res.data) {
        console.log("获取职位列表成功:", res.data)

        // 处理职位列表数据
        const newList = res.data.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedJobList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedJobList = [...this.data.jobList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedJobList = newList
        }

        // 更新页面数据
        this.setData({
          jobList: updatedJobList,
          hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          isLoading: false,
          isRequest: true,
        })

        return res.data
      } else {
        console.error("获取职位列表失败:", res)
        this.setData({
          isLoading: false,
          hasMore: false,
        })
        return null
      }
    } catch (error) {
      console.error("请求职位列表异常:", error)
      this.setData({
        isLoading: false,
      })
      return null
    }
  },
  /**
   * 处理添加地区按钮点击事件
   */
  handleAddRegionClick() {
    // 跳转到地区选择页面，传递Tab类型
    ROUTER.navigateTo({
      path: "/pages/select/select-region/index",
      query: {
        tabType: "announcement",
      },
    })
  },
  /**
   * 处理菜单项点击事件
   * @param {Object} e 事件对象
   */
  async handleMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showPopupFilterMenu, jobSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key
    console.log(currentItem, "----------------------------")
    if (type == "page" && filterKey == "filter_list") {
      this.hidePopupMenuFilter()
      ROUTER.navigateTo({
        path: "/pages/select/select-job/index",
        query: {
          type: "list",
        },
      })
      return
    }
    const currentMenuSelected = jobSelectForTemplate[filterKey]

    if (type !== "apply_region") {
      this.setData({
        showRegionList: false,
      })
    }

    if (type === "check" || type === "apply_region") {
      this.hidePopupMenuFilter()
    } else if (
      showPopupFilterMenu === true &&
      this.data.activeExpanded === filterKey
    ) {
      this.hidePopupMenuFilter()
    } else {
      this.showPopupMenuFilter()
    }

    this.setData({
      activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
    if (filterKey === "apply_region") {
      // 切换地区列表显示状态
      this.setData({
        showRegionList: !this.data.showRegionList,
      })
      return
    }

    // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "check") {
      this.setData({
        [`jobSelectForTemplate.${filterKey}`]: handleMultiSelect(
          currentMenuSelected || [],
          1
        ),
      })
      console.log("123123123123", filterKey)
      if (filterKey === "fit_me") {
        this.handleFitMeFilter()
      }
      setJobSelectForTemplateCache(this.data.jobSelectForTemplate)
      this.applyFilter()
      return
    }
  },
  /**
   * 处理"适合我"筛选逻辑
   */
  handleFitMeFilter() {
    const { jobSelectForTemplate, jobMenuData } = this.data
    const fitMeSelected = jobSelectForTemplate.fit_me || []
    const isFitMeEnabled = fitMeSelected.includes(1)

    console.log("处理适合我筛选:", { isFitMeEnabled, fitMeSelected })

    if (isFitMeEnabled) {
      // 获取匹配筛选条件
      const matchFilter = jobMenuData?.fit_me?.match_filter
      if (!matchFilter) {
        console.warn("未找到适合我的匹配筛选条件")
        return
      }

      console.log("适合我匹配条件:", matchFilter)

      // 获取筛选配置数据，用于验证值是否存在
      const filterListData = this.getFilterListConfigMap()

      // 更新筛选条件
      const updatedFilterList = { ...jobSelectForTemplate.filter_list }

      // 遍历匹配筛选条件
      Object.keys(matchFilter).forEach((filterKey) => {
        const matchValues = matchFilter[filterKey]

        // 将字符串转换为数组
        const valuesArray =
          typeof matchValues === "string"
            ? matchValues.split(",").map((v) => parseInt(v.trim()))
            : matchValues

        console.log(`处理筛选项 ${filterKey}:`, valuesArray)

        // 验证值是否在配置中存在
        const validValues = this.validateFilterValues(
          filterKey,
          valuesArray,
          filterListData
        )

        if (validValues.length > 0) {
          updatedFilterList[filterKey] = validValues
          console.log(`设置 ${filterKey} 筛选条件:`, validValues)
        }
      })

      // 更新状态
      this.setData({
        [`jobSelectForTemplate.filter_list`]: updatedFilterList,
      })

      console.log("适合我筛选已应用:", updatedFilterList)
    } else {
      // 取消适合我筛选 - 只取消 matchFilter 中对应的筛选条件
      const matchFilter = jobMenuData?.fit_me?.match_filter
      if (!matchFilter) {
        console.warn("未找到适合我的匹配筛选条件，无法取消")
        return
      }

      console.log("取消适合我匹配条件:", matchFilter)

      // 获取当前筛选条件
      const currentFilterList = { ...jobSelectForTemplate.filter_list }

      // 只取消 matchFilter 中对应的筛选项
      Object.keys(matchFilter).forEach((filterKey) => {
        const matchValues = matchFilter[filterKey]

        // 将字符串转换为数组
        const valuesArray =
          typeof matchValues === "string"
            ? matchValues.split(",").map((v) => parseInt(v.trim()))
            : matchValues

        console.log(`取消筛选项 ${filterKey}:`, valuesArray)

        // 从当前筛选条件中移除匹配的值
        if (
          currentFilterList[filterKey] &&
          Array.isArray(currentFilterList[filterKey])
        ) {
          const filteredValues = currentFilterList[filterKey].filter(
            (value) => !valuesArray.includes(value)
          )
          currentFilterList[filterKey] = filteredValues
          console.log(`${filterKey} 筛选条件更新为:`, filteredValues)
        }
      })

      // 更新状态
      this.setData({
        [`jobSelectForTemplate.filter_list`]: currentFilterList,
      })

      console.log("已取消适合我相关筛选条件:", currentFilterList)
    }
  },

  /**
   * 获取筛选配置的映射表
   * @returns {Object} 筛选配置映射 { filterKey: [可用的value数组] }
   */
  getFilterListConfigMap() {
    const filterConfig = {}
    const filterListData = this.data.jobMenuData?.filter_list?.data || []

    filterListData.forEach((category) => {
      category.list?.forEach((filterItem) => {
        const filterKey = filterItem.filter_key
        const availableValues = filterItem.list?.map((item) => item.value) || []
        filterConfig[filterKey] = availableValues
      })
    })

    console.log("筛选配置映射:", filterConfig)
    return filterConfig
  },

  /**
   * 验证筛选值是否在配置中存在
   * @param {string} filterKey 筛选项键名
   * @param {Array} values 要验证的值数组
   * @param {Object} filterConfig 筛选配置映射
   * @returns {Array} 有效的值数组
   */
  validateFilterValues(filterKey, values, filterConfig) {
    const availableValues = filterConfig[filterKey] || []
    const validValues = values.filter((value) =>
      availableValues.includes(value)
    )

    if (validValues.length !== values.length) {
      const invalidValues = values.filter(
        (value) => !availableValues.includes(value)
      )
      console.warn(`筛选项 ${filterKey} 中的无效值:`, invalidValues)
      console.log(`筛选项 ${filterKey} 的可用值:`, availableValues)
    }

    return validValues
  },
  /**
   * 从缓存恢复筛选条件
   */
  async restoreFilterFromCache() {
    await this.updateJobSelectForTemplateFromCache()
  },
  /**
   * 从缓存恢复公告选中状态 (jobSelectForTemplate)
   */
  updateJobSelectForTemplateFromCache() {
    const jobSelectForTemplate = this.data.jobSelectForTemplate
    const cacheJobSelectForTemplate = getJobSelectForTemplateCache()
    const cacheNoticeSelectForTemplate =
      getNoticeSelectForTemplateCache("announcement")
    for (const key in jobSelectForTemplate) {
      if (cacheJobSelectForTemplate[key]) {
        jobSelectForTemplate[key] = cacheJobSelectForTemplate[key]
      }
      if (key == "apply_region" && !cacheJobSelectForTemplate[key]) {
        jobSelectForTemplate[key] = ["0-0-0"]
      }
    }
    this.setData({
      jobSelectForTemplate,
    })

    // 检查缓存中是否有"适合我"的选择，如果有则重新应用匹配条件
    const fitMeSelected = jobSelectForTemplate?.fit_me || []
    const isFitMeEnabled = fitMeSelected.includes(1)

    if (isFitMeEnabled) {
      console.log("缓存中发现适合我已选中，重新应用匹配条件")
      // 延迟执行，确保jobMenuData已经设置完成
      setTimeout(() => {
        this.handleFitMeFilter()
        // 重新保存到缓存，确保最新的匹配条件被应用
        setJobSelectForTemplateCache(this.data.jobSelectForTemplate)
      }, 0)
    }
  },
  handleJobMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`jobSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setJobSelectForTemplateCache(this.data.jobSelectForTemplate)
    this.hidePopupMenuFilter()
    this.applyFilter()
  },
  goResume() {
    ROUTER.navigateTo({
      path: "/pages/my/resume/index",
    })
  },
  // 设置菜单sticky的top值
  setMenuStickyTop() {
    const query = wx.createSelectorQuery()
    query
      .select("#commonHeader")
      .boundingClientRect((headerRect) => {
        let headerHeight = 100 // 默认高度
        console.log("获取到的header信息:", headerRect)

        if (headerRect) {
          headerHeight = headerRect.height
          console.log("成功获取导航栏高度:", headerHeight)
        } else {
          console.log("无法获取导航栏高度，使用降级方案")
          // 降级方案：通过系统信息计算
          const systemInfo = wx.getSystemInfoSync()
          const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
          headerHeight = menuButtonInfo.bottom
          console.log("降级方案计算高度:", headerHeight)
        }

        // 获取菜单容器的位置信息
        const menuQuery = wx.createSelectorQuery()
        menuQuery.select(".main-top").boundingClientRect()
        menuQuery.selectViewport().scrollOffset()
        menuQuery.exec((res) => {
          const menuRect = res[0]
          const scrollOffset = res[1]

          let menuOffsetTop = 0
          if (menuRect) {
            // 计算菜单距离页面顶部的距离
            menuOffsetTop = menuRect.top + scrollOffset.scrollTop
          }

          // 设置数据
          this.setData({
            headerHeight: headerHeight,
            menuOffsetTop: menuOffsetTop,
          })

          console.log("导航栏高度:", headerHeight)
          console.log("菜单初始位置:", menuOffsetTop)
        })
      })
      .exec()
  },
  handlePopupClose() {
    console.log("弹窗关闭事件")
    // 清空展开状态
    this.setData({
      activeExpanded: "",
      showRegionList: false,
    })

    // 调用hidePopupMenuFilter真正关闭弹窗（包含恢复备份的逻辑）
    this.hidePopupMenuFilter()
  },

  goSearch() {
    ROUTER.navigateTo({
      path: "/pages/search/index",
    })
  },

  onReachBottom() {
    console.log("触底了")
    // 检查是否还有更多数据且当前不在加载中
    if (this.data.hasMore && !this.data.isLoading) {
      // 页码+1
      const nextPage = this.data.page + 1
      this.setData({
        page: nextPage,
      })

      // 加载下一页数据
      const apiParams = this.buildApiParams(this.data.jobSelectForTemplate)
      this.getJobList(apiParams, true)
    }
  },
  onShareAppMessage() {},

  /**
   * 页面滚动事件处理
   * @param {Object} e 滚动事件对象
   */
  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }
    const scrollTop = e.scrollTop
    if (scrollTop > 0) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }

    // 这里可以添加其他滚动处理逻辑，比如导航栏背景色变化等
  },
})

Page(pageConfig)
